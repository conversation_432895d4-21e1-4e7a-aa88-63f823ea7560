#selectAll{data: {locale: t('activerecord.attributes.admin.select_all')}}
#unselectAll{data: {locale: t('activerecord.attributes.admin.drop_all')}}

.card
  .card-body
    = simple_form_for([:admin, @admin], wrapper: :horizontal_form) do |f|
      = f.error_notification
      .row.mb-3
        .col
          = f.input :name_surname, label: 'Ad Soyad'
          = f.input :email, label: 'E-posta', required: true
          = f.input :gsm, label: 'Telefon', class: 'form-control', required: true
          - if is_access?('co_admin', 'security')
            = f.input :ip_address, label: "Sisteme Giriş Yapılacak IP Adresi", input_html: { class:"form-control", id: 'ip_address'},  hint: 'Birden fazla ekleyebilmek için IP adresleri arasında boşluk bırakılması yeterlidir. Örnek: *********** ***********'
            = f.input :flexible_login, as: :select, label: "IP'den Bağımsız Giriş", input_html: { class:"form-control", id: 'flexible_login'}, hint: 'Evet durumunda IP kontrolü yapılmayacaktır.'

          - if is_access?('co_admin', 'send_invoice')
            .row
              .col-auto
                = f.input :send_merchant_invoice_mail, label: 'Üye Fatura Bilgilerini Gönder (Mail)'
              .col
                = f.input :send_merchant_invoice_sms, label: 'Üye Fatura Bilgilerini Gönder (Sms)'

          = f.input :admin_type, label: 'Yetki', collection: $co_admin_types, include_blank: true, required: true

          - if is_access?('co_admin', 'remove_approval')
            = f.input :disapproved, as: :boolean, label: "KVKK Onayını Kaldır"

          = f.input :origin_id, label: 'Origin', collection: Origin.all.pluck(:name, :id), required: true

          - if f.object.admin_type == "customer_cari"
            - a_type_collection = [["Müşteri", 0], ["Müşteri Ve Üye İşyeri", 2]]
          - elsif f.object.admin_type == "merchant_cari"
            - a_type_collection = [["Üye İşyeri", 1], ["Müşteri Ve Üye İşyeri", 2]]
          - else
            - a_type_collection = []

          = f.input :a_type, label: 'Cari Türü*', collection: a_type_collection, value: f.object.a_type


          = f.input :cust_merch_c_ids, collection: CAccount.where(a_type: 2).pluck(:c_name, :id), label: 'Müşteri Ve Üye İşyeri Cari', required: false, value: f.object.cust_merch_c_ids, input_html: { multiple: true}

          = f.input :merchant_id, collection: Merchant.order(:name).all, label: 'Üye İşyeri', input_html: { class: 'select2-merchant' }

          = f.input :merchant_c_ids, collection: CAccount.where(a_type: 1).pluck(:c_name, :id), label: 'Üye İşyeri Cari', required: false, input_html: { multiple: true}
          - merchant_id = Merchant.where(id: f.object.merchant_ids)
          = f.input :merchant_ids, collection: merchant_id, label: 'Üye İşyeri', input_html: { multiple: true, id: 'multiple-merchant'}

          = f.input :customer_c_ids, collection: CAccount.where(a_type: 0).pluck(:c_name, :id), label: 'Müşteri Cari', required: false, input_html: { multiple: true}

          - customer_id = Customer.where(id: f.object.customer_ids)
          = f.input :customer_ids, collection: customer_id, label: 'Müşteri', input_html: { multiple: true, id: 'multiple-customer'}

          = f.input :merchant_branch_id, as: :select, collection: [], label: 'Üye İşyeri Şube', input_html: { class: 'select2-merchant-branch' }
          =# f.input :customer_id, collection: Customer.order(:name).all, label: 'Müşteri', input_html: { class: 'select2-customer' }

          = f.input :customer_branch_ids, as: :select, collection: [], label: 'Müşteri Şubeleri', input_html: { multiple: true, class: 'select2-merchant-branches' }

          = f.input :authority, label: 'İşlem Yetkisi', collection: [], input_html: { multiple: true, id: 'authority' }

          - if is_access?('co_admin', 'is_active')
            = f.input :is_active, as: :boolean, label: 'Aktif Hesap mı?'

          - if is_access?('co_admin', 'new_system_user')
            .new-system-user-checkbox.d-none
              = f.input :new_system_user, as: :boolean, label: 'Yeni Sistem Kullanıcısı mı?'

          = f.input :is_transferable, as: :boolean, label: 'Transfer Kart yetkisi var mı?'

      - if is_access?('co_admin', 'update')
        %div
          = f.button :submit, 'Kaydet', class: "btn-primary submit-btn"

:css
  .select2-results__option[aria-selected=true] {
    display: none;
  }
  .select2-results__group {
    opacity: 0.75;
  }
  .select2-results__options--nested {
    margin-left: 20px;
  }
  .sweetify{
    display: none;
  }
:javascript
  function showMerchants(id) {
    $("#myModal").modal();
  };

  function cariAuthority() {
    if ($("#co_admin_admin_type").val() == "merchant_cari") {
      $(".co_admin_send_merchant_invoice_mail").removeClass("d-none");
      $(".co_admin_send_merchant_invoice_sms").removeClass("d-none");
      $(".co_admin_merchant_id").addClass("d-none");
      $(".co_admin_merchant_branch_id").addClass("d-none");
      $(".co_admin_customer_branch_ids").addClass("d-none");
      $("#co_admin_customer_branch_ids").prop("required", false);

      $(".co_admin_customer_id").addClass("d-none");
      $(".co_admin_customer_branch_id").addClass("d-none");
      $(".co_admin_merchant_ids").addClass("d-none");
      $(".co_admin_customer_ids").addClass("d-none");
      $(".co_admin_merchant_c_ids").addClass("d-none");
      $(".co_admin_customer_c_ids").addClass("d-none");
      $(".co_admin_cust_merch_c_ids").addClass("d-none");
      $(".co_admin_a_type").removeClass("d-none")
      $("#co_admin_a_type").prop("required", true);
    } else if ($("#co_admin_admin_type").val() == "customer_cari") {
      $("#co_admin_customer_branch_ids").prop("required", false);
      $(".co_admin_send_merchant_invoice_mail").addClass("d-none");
      $(".co_admin_send_merchant_invoice_sms").addClass("d-none");
      $(".co_admin_merchant_id").addClass("d-none");
      $(".co_admin_merchant_branch_id").addClass("d-none");
      $(".co_admin_customer_id").addClass("d-none");
      $(".co_admin_customer_branch_id").addClass("d-none");
      $(".co_admin_customer_branch_ids").addClass("d-none");

      $(".co_admin_merchant_c_ids").addClass("d-none");
      $(".co_admin_merchant_ids").addClass("d-none");
      $(".co_admin_customer_ids").addClass("d-none");
      $(".co_admin_customer_c_ids").addClass("d-none");
      $(".co_admin_cust_merch_c_ids").addClass("d-none");
      $(".co_admin_a_type").removeClass("d-none");
      $("#co_admin_a_type").prop("required", true);
    } else if ($("#co_admin_admin_type").val() == "customer_branch"){
      $("#co_admin_customer_branch_ids").prop("required", true);
      $(".co_admin_send_merchant_invoice_mail").addClass("d-none");
      $(".co_admin_customer_branch_ids").removeClass("d-none");
      $(".co_admin_send_merchant_invoice_sms").addClass("d-none");
      $("#select_customer").prop("required", true);
      $("#select2_customer_branch").prop("required", true);
      $(".select_customer_branch").removeClass("d-none");
      $(".co_admin_merchant_id").removeClass("d-none");
      $(".co_admin_merchant_branch_id").addClass("d-none");
      $(".co_admin_customer_id").addClass("d-none");
      $(".co_admin_customer_branch_id").removeClass("d-none");
      $(".co_admin_merchant_ids").addClass("d-none");
      $(".co_admin_customer_ids").removeClass("d-none");
      $(".co_admin_merchant_c_ids").addClass("d-none");
      $(".co_admin_customer_c_ids").addClass("d-none");
      $(".co_admin_cust_merch_c_ids").addClass("d-none");
      $(".co_admin_a_type").addClass("d-none");
      $(".co_admin_merchant_id").hide();
      $(".co_admin_customer_id").show();
      $("#co_admin_a_type").prop("required", false);

    } else if ($("#co_admin_admin_type").val() == "merchant_branch"){
      $(".co_admin_send_merchant_invoice_mail").addClass("d-none");
      $(".co_admin_send_merchant_invoice_sms").addClass("d-none");
      $(".co_admin_customer_branch_ids").addClass("d-none");
      $("#co_admin_customer_branch_ids").prop("required", false);

      $("#select_merchant").prop("required", true);
      $("#select2_merchant_branch").prop("required", true);
      $(".select_merchant_branch").removeClass("d-none");
      $(".co_admin_merchant_id").removeClass("d-none");
      $(".co_admin_merchant_branch_id").removeClass("d-none");
      $(".co_admin_customer_id").removeClass("d-none");
      $(".co_admin_customer_branch_id").addClass("d-none");
      $(".co_admin_merchant_ids").addClass("d-none");
      $(".co_admin_customer_ids").addClass("d-none");
      $(".co_admin_merchant_c_ids").addClass("d-none");
      $(".co_admin_customer_c_ids").addClass("d-none");
      $(".co_admin_cust_merch_c_ids").addClass("d-none");
      $(".co_admin_a_type").addClass("d-none");
      $(".co_admin_merchant_id").show();
      $(".co_admin_customer_id").hide();
      $("#co_admin_a_type").prop("required", false);

    } else {
      $(".co_admin_customer_branch_ids").addClass("d-none");
      $("#co_admin_customer_branch_ids").prop("required", false);
      $(".co_admin_merchant_id").addClass("d-none");
      $(".co_admin_merchant_branch_id").addClass("d-none");
      $(".co_admin_customer_id").addClass("d-none");
      $(".co_admin_customer_branch_id").addClass("d-none");
      $(".co_admin_merchant_ids").addClass("d-none");
      $(".co_admin_customer_ids").addClass("d-none");
      $(".co_admin_merchant_c_ids").addClass("d-none");
      $(".co_admin_customer_c_ids").addClass("d-none");
      $(".co_admin_cust_merch_c_ids").addClass("d-none");
      $(".co_admin_a_type").addClass("d-none");
      $("#co_admin_a_type").prop("required", false);

    }
  };

  function change_admin_type() {
    admin_type =  $("#co_admin_admin_type").val();
    if (admin_type == "customer_branch"){
      select_customer_branches = $("#co_admin_customer_branch_ids");
      customer_id = $("#multiple-customer").select2({
        dropdownAdapter: $.fn.select2.amd.require('select2/selectAllAdapter'),
        allowClear: true
      }).on('select2:select select2:unselecting', function (e) {
        select_customer_branches.val(null).trigger('change');
      });
      customer_id.empty();
      origin_id = $("#co_admin_origin_id");

      if (origin_id.val() != ""){
        $.ajax({
          url: "#{select2_cust_admin_co_admins_path(format: :json)}",
          delay: 250,
          dataType: 'json',
          data: {
            "origin_id": $('#co_admin_origin_id').val()
          },
          success: function (data) {
            customer_id.empty();
            if((data.results !== null)){
              results = data.results
              for (var i = 0; i < results.length; i++) {
                customer_id.append('<option id=' + results[i][1] + ' value=' + results[i][1] + '>' + $.fn.dataTable.render.text().display((results[i][0])) + '</option>');
              }
            }
          }
        })
      }

    }
    else if (admin_type == "merchant_branch"){
      merchant_id = $("#co_admin_merchant_id");
      merchant_id.empty();
      origin_id = $("#co_admin_origin_id");

      if (origin_id.val() != ""){
        $.ajax({
          url: "#{select2_merch_admin_co_admins_path(format: :json)}",
          delay: 250,
          dataType: 'json',
          data: {
            "origin_id": $('#co_admin_origin_id').val()
          },
          success: function (data) {
            merchant_id.empty();
            merchant_id.append('<option value="">Üye İşyeri Seçiniz</option>');
            if((data.results !== null)){
              results = data.results
              for (var i = 0; i < results.length; i++) {
                merchant_id.append('<option id=' + results[i][1] + ' value=' + results[i][1] + '>' + $.fn.dataTable.render.text().display((results[i][0])) + '</option>');
              }
            }
          }
        })
      }
    }
  }

  function customerAuthority() {
    if ($("#co_admin_admin_type").val() == "customer_cari") {
       $(".co_admin_disapproved").removeClass("d-none");
    } else if ($("#co_admin_admin_type").val() == "customer_branch") {
      $(".co_admin_disapproved").removeClass("d-none");
    } else {
      $(".co_admin_disapproved").addClass("d-none");
    };
  };

  function cari_type(){
    //Müşteri seçili ise
    if ($("#co_admin_a_type").val() == "0") {
      $(".co_admin_customer_ids").removeClass("d-none");
      $(".co_admin_customer_c_ids").removeClass("d-none");
      $(".co_admin_cust_merch_c_ids").addClass("d-none");
      $(".co_admin_merchant_ids").addClass("d-none");
      $("#multiple-merchant").val("");
      $("#co_admin_cust_merch_c_ids").val("");

      select_customer_cari= $("#co_admin_customer_c_ids").select2();
      select_customer_cari.empty();
      origin_id = $("#co_admin_origin_id")

      if (origin_id.val() != ""){
        $.ajax({
          url: "#{select2_c_account_admin_co_admins_path(format: :json)}",
          delay: 250,
          dataType: 'json',
          data: {
            "origin_id": origin_id.val(),
            "c_account_type": $('#co_admin_a_type').val()
          },
          success: function (data) {

            select_customer_cari.empty();
            select_customer_cari.append('<option value="">Müşteri Cari Seçiniz</option>');
            if((data.results !== null)){
              results = data.results

              for (var i = 0; i < results.length; i++) {
                select_customer_cari.append('<option id=' + results[i][1] + ' value=' + results[i][1] + '>' + $.fn.dataTable.render.text().display((results[i][0])) + '</option>');
              }
            }
          }
        })
      }

      select_customer_id = $("#multiple-customer").select2();

      select_customer_id.val(null);
      select_customer_cari.select2({
        placeholder: "Müşteri Cari Seçiniz!",
        allowClear: true
      }).on('select2:select', function (e) {
        select_customer_id.trigger('change');
      });

      select_customer_id.select2({
        placeholder: "Müşteri Seçiniz!",
        allowClear: true,
        ajax: {
          url: "#{ select2_customer_admin_co_admins_path(format: :json)}",
          delay: 250,
          type: 'POST',
          dataType: 'json',
          data: function (params) {
            var queryParameters = {
              q: params.term,
              customer_id: select_customer_cari.val()
            }
            return queryParameters;
          }
        }
      })

    } else if ($("#co_admin_a_type").val() == "1") {
      //Üye işyeri seçili ise
      $(".co_admin_merchant_ids").removeClass("d-none");
      $(".co_admin_merchant_c_ids").removeClass("d-none");
      $(".co_admin_customer_c_ids").addClass("d-none");
      $(".co_admin_cust_merch_c_ids").addClass("d-none");
      $(".co_admin_customer_ids").addClass("d-none");
      $("#multiple-customer").val("");
      $("#co_admin_cust_merch_c_ids").val("");

      select_merchant_cari = $("#co_admin_merchant_c_ids").select2();

      select_merchant_cari.empty();
      origin_id = $("#co_admin_origin_id")

      if (origin_id.val() != ""){
        $.ajax({
          url: "#{select2_c_account_admin_co_admins_path(format: :json)}",
          delay: 250,
          dataType: 'json',
          data: {
            "origin_id": origin_id.val(),
            "c_account_type": $('#co_admin_a_type').val()
          },
          success: function (data) {

            select_merchant_cari.empty();
            select_merchant_cari.append('<option value="">Müşteri Cari Seçiniz</option>');
            if((data.results !== null)){
              results = data.results

              for (var i = 0; i < results.length; i++) {
                select_merchant_cari.append('<option id=' + results[i][1] + ' value=' + results[i][1] + '>' + $.fn.dataTable.render.text().display((results[i][0])) + '</option>');
              }
            }
          }
        })
      }

      select_merchant_id = $("#multiple-merchant").select2();
      select_merchant_id.val(null);
      select_merchant_cari.select2({
        placeholder: "Cari Seçiniz!",
        allowClear: true
      }).on('select2:select', function (e) {
        select_merchant_id.val(null).trigger('change');
      });

      select_merchant_id.select2({
        placeholder: "Üye İşyeri Seçiniz!",
        allowClear: true,
        ajax: {
          url: "#{ select2_merchant_admin_co_admins_path(format: :json)}",
          delay: 250,
          type: 'GET',
          dataType: 'json',
          data: function (params) {
            var queryParameters = {
              q: params.term,
              merchant_id: select_merchant_cari.val()
            }
            return queryParameters;
          }
        }
      });
    } else if ($("#co_admin_a_type").val() == "2") {
      //Müşteri + Üye işyeri seçili ise
      $(".co_admin_customer_c_ids").addClass("d-none");
      $("#co_admin_customer_c_ids").val("");
      $("#co_admin_merchant_c_ids").val("");

      select_cust_merch_cari= $("#co_admin_cust_merch_c_ids").select2();
      select_cust_merch_cari.empty();
      origin_id = $("#co_admin_origin_id")

      if (origin_id.val() != ""){
        $.ajax({
          url: "#{select2_c_account_admin_co_admins_path(format: :json)}",
          delay: 250,
          dataType: 'json',
          data: {
            "origin_id": origin_id.val(),
            "c_account_type": $('#co_admin_a_type').val()
          },
          success: function (data) {
            select_cust_merch_cari.empty();
            select_cust_merch_cari.append('<option value="">Müşteri Cari Seçiniz</option>');
            if((data.results !== null)){
              results = data.results

              for (var i = 0; i < results.length; i++) {
                select_cust_merch_cari.append('<option id=' + results[i][1] + ' value=' + results[i][1] + '>' + $.fn.dataTable.render.text().display((results[i][0])) + '</option>');
              }
            }
          }
        })
      }

      if ($("#co_admin_admin_type").val() == "merchant_cari") {
        $(".co_admin_merchant_ids").removeClass("d-none");
        $(".co_admin_customer_ids").addClass("d-none");
        select_merchant_id = $("#multiple-merchant").select2();
        select_merchant_id.val(null);
        select_cust_merch_cari.select2({
          placeholder: "",
          allowClear: true
        }).on('select2:select', function (e) {
          select_merchant_id.trigger('change')
        });

        select_merchant_id.select2({
          placeholder: "Üye İşyeri Seçiniz!",
          allowClear: true,
          ajax: {
            url: "#{ select2_merchant_admin_co_admins_path(format: :json)}",
            delay: 250,
            type: 'GET',
            dataType: 'json',
            data: function (params) {
              var queryParameters = {
                q: params.term,
                merchant_id: select_cust_merch_cari.val()
              }
              return queryParameters;
            }
          }
        });
      };

      if ($("#co_admin_admin_type").val() == "customer_cari") {
        $(".co_admin_merchant_ids").addClass("d-none");
        $(".co_admin_customer_ids").removeClass("d-none");

        select_customer_id = $("#multiple-customer").select2();
        select_customer_id.val(null);
        select_cust_merch_cari.select2({
          placeholder: "",
          allowClear: true
        }).on('select2:select', function (e) {
          select_customer_id.trigger('change')
        });

        select_customer_id.select2({
          placeholder: "Müşteri Seçiniz!",
          allowClear: true,
          ajax: {
            url: "#{ select2_customer_admin_co_admins_path(format: :json)}",
            delay: 250,
            type: 'POST',
            dataType: 'json',
            data: function (params) {
              var queryParameters = {
                q: params.term,
                customer_id: select_cust_merch_cari.val()
              }
              return queryParameters;
            }
          }
        })
      }

      $(".co_admin_cust_merch_c_ids").removeClass("d-none")
      $(".co_admin_merchant_c_ids").addClass("d-none")
    }
  };

  function cariAuthorityCollection() {
    //işlem yetkisi düzenleme
    $.ajax({
      url: "#{select2_search_authority_admin_co_admins_path(format: :json)}",
      delay: 250,
      dataType: 'json',
      data: {
        "origin_id": $('#co_admin_origin_id').val(),
        "admin_type": $("#co_admin_admin_type").val()
      },
      success: function (data) {
        var $authority = $("#authority");
        $authority.empty();
        if (data.results !== null){
          results = data.results;
          results.forEach(function(val) {
            $('#authority').append(new Option(val));
          });
        }
      }
    })
  }

  r(function(){
    select_customer_branches = $("#co_admin_customer_branch_ids");
    customer_id = $("#multiple-customer").select2({
      dropdownAdapter: $.fn.select2.amd.require('select2/selectAllAdapter'),
      allowClear: true
    }).on('select2:select select2:unselecting', function (e) {
      select_customer_branches.val(null).trigger('change');
    });

    $("#authority").select2({
      dropdownAdapter: $.fn.select2.amd.require('select2/selectAllAdapter'),
      allowClear: true
    })

    $('#co_admin_origin_id').select2()

    $('#new_co_admin').on('submit', function(e){
      $('#co_admin_gsm').unmask();
    });

    var flexible_login = $('#flexible_login').val();
    if(flexible_login == "true") {
      $('#ip_address').val(null);
      $('#ip_address').prop('disabled', 'disabled');
    } else {
      $('#ip_address').prop('disabled', false);
    };

    $('#flexible_login').on('change', function() {
      if($('#flexible_login').val() == "true") {
        $('#ip_address').val(null);
        $('#ip_address').prop('disabled', 'disabled');
      } else {
        $('#ip_address').prop('disabled', false);
      };
    });

    var ip_address = $('#ip_address').val() || [];
    if(ip_address.length) {
      $('#flexible_login').val("false");
    };

    $('#ip_address').on('change', function() {
      if($('#ip_address').val().length) {
        $('#flexible_login').val("false");
      };
    });

    var send_merchant_invoice_sms = $("#co_admin_send_merchant_invoice_sms");
    send_merchant_invoice_sms.on('change', function(){
      if (send_merchant_invoice_sms.is(":checked")){
        $("#co_admin_gsm").prop('required', true);
      }
      else{
        $("#co_admin_gsm").prop('required', false);
      }
    });

    var flexible_login = $('#flexible_login').val();
    if(flexible_login == "true") {
      $('#ip_address').val(null);
      $('#ip_address').prop('disabled', 'disabled');
    } else {
      $('#ip_address').prop('disabled', false);
    };

    $('#flexible_login').on('change', function() {
      if($('#flexible_login').val() == "true") {
        $('#ip_address').val(null);
        $('#ip_address').prop('disabled', 'disabled');
      } else {
        $('#ip_address').prop('disabled', false);
      };
    });

    var ip_address = $('#ip_address').val() || [];
    if(ip_address.length) {
      $('#flexible_login').val("false");
    };

    $('#ip_address').on('change', function() {
      if($('#ip_address').val().length) {
        $('#flexible_login').val("false");
      };
    });

    $("#co_admin_a_type").select2({
      allowClear: true,
      placeholder: ''
    });

    $("#co_admin_cust_merch_c_ids").select2({
      allowClear: true,
      placeholder: ''
    });

    $("#admin_approval_user_id").select2({
      allowClear: true,
      placeholder: ''
    });

    $("#co_admin_admin_type").select2({
      allowClear: true,
      placeholder: ''
    });

    $("#flexible_login").select2({
      allowClear: true,
      placeholder: ''
    });

    select_customer_cari= $("#co_admin_customer_c_ids");
    $('#co_admin_origin_id').change(function() {
      if ($(this).val() == "1") {
        $('.new-system-user-checkbox').removeClass('d-none');
      } else {
        $('.new-system-user-checkbox').addClass('d-none');
        $('#co_admin_new_system_user').prop('checked', false);
      }
      
      //işlem yetkisi düzenleme
      if ($("#co_admin_admin_type").val() != ""){
        cariAuthorityCollection();
      }

      // Origine göre cari getirme
      cari_type();

      //admin tipine göre işlem yetkisi getirme
      change_admin_type();
    })

    customerAuthority();
    cariAuthority();

    $("#co_admin_admin_type").change(function() {
      $("#co_admin_customer_id").val(null).trigger('change');
      $("#co_admin_customer_branch_id").val(null).trigger('change');
      $("#co_admin_customer_branch_ids").val(null).trigger('change');
      $("#co_admin_a_type").val(null).trigger('change');

      customerAuthority();
      cariAuthority();
      change_admin_type();

      //işlem yetkisi düzenleme
      if ($("#co_admin_origin_id").val() != ""){
        cariAuthorityCollection();
      }

      if ($("#co_admin_admin_type").val() == "merchant_cari") {
        select_a_type = $("#co_admin_a_type").select2()
        select_a_type.html("")
        select_a_type.append('<option></option>');
        select_a_type.append('<option value="1" >'+"Üye İşyeri"+' </option>');
        select_a_type.append('<option value="2">'+"Müşteri Ve Üye İşyeri"+' </option>');
      } else if ($("#co_admin_admin_type").val() == "customer_cari") {
        select_a_type = $("#co_admin_a_type").select2()
        select_a_type.html("")
        select_a_type.append('<option></option>');
        select_a_type.append('<option value="0">'+"Müşteri"+' </option>');
        select_a_type.append('<option value="2">'+"Müşteri Ve Üye İşyeri"+' </option>');
      }
    });

    cari_type()
    $("#co_admin_a_type").change(function() {
      cari_type();
    });

    var select_merchant = $(".select2-merchant");
    var select_merchant_branch = $(".select2-merchant-branch");
    var select_customer = $(".select2-customer");
    var select_customer_branches = $("#co_admin_customer_branch_ids");
    select_customer_branches.select2();

    select_merchant.select2({
      placeholder: "Üye İşyeri Seçiniz!",
      allowClear: true
    }).on('select2:select', function (e) {
      select_merchant_branch.val(null).trigger('change');
    });

    select_customer.select2({
      placeholder: "Müşteri Seçiniz!",
      allowClear: true
    }).on('select2:select', function (e) {
      select_customer_branches.val(null).trigger('change');
    });

    select_customer_branches.select2({
      placeholder: "Şube Seçiniz!",
      allowClear: true,
      dropdownAdapter: $.fn.select2.amd.require('select2/selectAllAdapter'),
      ajax: {
        url: "#{select2_cust_branches_admin_co_admins_path(format: :json)}",
        delay: 250,
        dataType: 'json',
        data: function (params) {
          var queryParameters = {
            customer_ids: function() { return $("#multiple-customer").val() }
          }
          return queryParameters;
        }
      }
    })
    select_merchant_branch.select2({
      placeholder: "Şube Seçiniz!",
      allowClear: true,
      ajax: {
        url: "#{select2_search_admin_merchant_branches_path(format: :json)}",
        delay: 250,
        dataType: 'json',
        data: function (params) {
          var queryParameters = {
            q: params.term,
            merchant_id: function() { return select_merchant.val() }
          }
          return queryParameters;
        }
      }
    });

  })
